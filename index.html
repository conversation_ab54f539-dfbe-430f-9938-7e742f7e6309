<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComfyUI API 电商产品背景替换</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .left-panel, .right-panel {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
        }

        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            border-bottom: 3px solid #4facfe;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #555;
        }

        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            margin: 10px 5px;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }

        .status-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #2196f3;
        }

        .status-text {
            font-weight: bold;
            color: #1976d2;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .image-upload {
            border: 2px dashed #4facfe;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .image-upload:hover {
            background-color: #f0f8ff;
        }

        .image-upload.dragover {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }

        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            margin: 10px 0;
        }

        .result-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .result-image {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .result-image img {
            width: 100%;
            height: auto;
            display: block;
        }

        .log-section {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 ComfyUI API 控制台</h1>
            <p>电商产品背景替换 - 直接调用 ComfyUI API 生成图片</p>
        </div>

        <div class="main-content">
            <!-- 左侧面板 - 输入控制 -->
            <div class="left-panel">
                <h2 class="section-title">🖼️ 图像上传</h2>
                <div class="image-upload" id="image-upload" onclick="document.getElementById('file-input').click()">
                    <input type="file" id="file-input" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
                    <div id="upload-text">
                        <p>📁 点击或拖拽上传产品图片</p>
                        <p style="font-size: 14px; color: #666; margin-top: 10px;">支持 JPG, PNG, WebP 格式</p>
                    </div>
                    <img id="preview-image" class="preview-image" style="display: none;">
                </div>

                <h2 class="section-title">📝 提示词设置</h2>
                <div class="form-group">
                    <label for="prompt-text">背景描述 (中文):</label>
                    <textarea id="prompt-text" placeholder="描述您想要的背景效果...">将图中的产品放在一块玻璃展板上，周围都是花朵，有化妆品的广告氛围</textarea>
                </div>

                <h2 class="section-title">⚙️ 生成参数</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="steps">采样步数:</label>
                        <input type="number" id="steps" value="8" min="1" max="50">
                    </div>
                    <div class="form-group">
                        <label for="cfg">CFG 强度:</label>
                        <input type="number" id="cfg" value="1" min="0.1" max="20" step="0.1">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="guidance">Flux 引导:</label>
                        <input type="number" id="guidance" value="2.5" min="0.1" max="10" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="seed">随机种子:</label>
                        <input type="number" id="seed" value="365395080461699">
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <button class="btn" id="generate-btn" onclick="generateImage()">🚀 生成背景图片</button>
                    <button class="btn btn-secondary" onclick="randomizeSeed()">🎲 随机种子</button>
                </div>
            </div>

            <!-- 右侧面板 - 状态和结果 -->
            <div class="right-panel">
                <h2 class="section-title">📊 生成状态</h2>
                <div class="status-section">
                    <div class="status-text" id="status-text">等待开始生成...</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div id="progress-text" style="font-size: 14px; color: #666; margin-top: 5px;">0%</div>
                </div>

                <h2 class="section-title">🖼️ 生成结果</h2>
                <div class="result-images" id="result-images">
                    <div style="text-align: center; color: #666; padding: 40px;">
                        <p>生成的图片将在这里显示</p>
                    </div>
                </div>

                <h2 class="section-title">📋 执行日志</h2>
                <div class="log-section" id="log-section">
等待操作...
                </div>
            </div>
        </div>
    </div>

    <script>
        // ComfyUI API 配置
        const COMFYUI_URL = 'https://8ee9184910a741fb9bd9bb258301313a--8188.ap-shanghai.cloudstudio.club';
        let uploadedImageName = null;
        let currentPromptId = null;

        // 工作流模板
        const workflowTemplate = {
            "2": {
                "inputs": {
                    "seed": 365395080461699,
                    "steps": 8,
                    "cfg": 1,
                    "sampler_name": "euler",
                    "scheduler": "simple",
                    "denoise": 1,
                    "model": ["22", 0],
                    "positive": ["3", 0],
                    "negative": ["7", 0],
                    "latent_image": ["10", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "K采样器"}
            },
            "3": {
                "inputs": {
                    "guidance": 2.5,
                    "conditioning": ["8", 0]
                },
                "class_type": "FluxGuidance",
                "_meta": {"title": "Flux引导"}
            },
            "5": {
                "inputs": {
                    "clip_name1": "clip_l.safetensors",
                    "clip_name2": "t5xxl_fp8_e4m3fn.safetensors",
                    "type": "flux",
                    "device": "default"
                },
                "class_type": "DualCLIPLoader",
                "_meta": {"title": "双CLIP加载器"}
            },
            "6": {
                "inputs": {"vae_name": "ae.sft"},
                "class_type": "VAELoader",
                "_meta": {"title": "加载VAE"}
            },
            "7": {
                "inputs": {"conditioning": ["9", 0]},
                "class_type": "ConditioningZeroOut",
                "_meta": {"title": "条件零化"}
            },
            "8": {
                "inputs": {
                    "conditioning": ["9", 0],
                    "latent": ["10", 0]
                },
                "class_type": "ReferenceLatent",
                "_meta": {"title": "ReferenceLatent"}
            },
            "9": {
                "inputs": {
                    "text": ["20", 0],
                    "speak_and_recognation": {"__value__": [false, true]},
                    "clip": ["5", 0]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}
            },
            "10": {
                "inputs": {
                    "pixels": ["18", 0],
                    "vae": ["6", 0]
                },
                "class_type": "VAEEncode",
                "_meta": {"title": "VAE编码"}
            },
            "11": {
                "inputs": {
                    "samples": ["2", 0],
                    "vae": ["6", 0]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE解码"}
            },
            "17": {
                "inputs": {
                    "image": "image (10).png",
                    "keep_alpha": false,
                    "🔄 refresh from Temp": null,
                    "🔄 refresh from Output": null
                },
                "class_type": "LG_LoadImage",
                "_meta": {"title": "🎈LG_加载图像"}
            },
            "18": {
                "inputs": {"image": ["17", 0]},
                "class_type": "FluxKontextImageScale",
                "_meta": {"title": "FluxKontextImageScale"}
            },
            "19": {
                "inputs": {"images": ["11", 0]},
                "class_type": "PreviewImage",
                "_meta": {"title": "预览图像"}
            },
            "20": {
                "inputs": {
                    "PreviewTextNode_0": "Place the product in the picture on a glass display board, surrounded by flowers, and has a cosmetics advertising atmosphere",
                    "text": ["24", 0]
                },
                "class_type": "PreviewTextNode",
                "_meta": {"title": "文本预览"}
            },
            "21": {
                "inputs": {
                    "model_path": "svdq/svdq-int4_r32-flux.1-kontext-dev.safetensors",
                    "cache_threshold": 0,
                    "attention": "nunchaku-fp16",
                    "cpu_offload": "enable",
                    "device_id": 0,
                    "data_type": "float16",
                    "i2f_mode": "enabled"
                },
                "class_type": "NunchakuFluxDiTLoader",
                "_meta": {"title": "Nunchaku FluxDiT 加载器"}
            },
            "22": {
                "inputs": {
                    "lora_name": "FLUX.1-Turbo-Alpha.safetensors",
                    "lora_strength": 1,
                    "model": ["21", 0]
                },
                "class_type": "NunchakuFluxLoraLoader",
                "_meta": {"title": "Nunchaku FluxLora 加载器"}
            },
            "23": {
                "inputs": {
                    "rgthree_comparer": {
                        "images": [
                            {
                                "name": "A",
                                "selected": true,
                                "url": "/api/view?filename=rgthree.compare._temp_jhjrh_00003_.png&type=temp&subfolder=&preview=jpeg&rand=0.4932305761510609"
                            },
                            {
                                "name": "B",
                                "selected": true,
                                "url": "/api/view?filename=rgthree.compare._temp_jhjrh_00004_.png&type=temp&subfolder=&preview=jpeg&rand=0.6381766265531946"
                            }
                        ]
                    },
                    "image_a": ["11", 0],
                    "image_b": ["18", 0]
                },
                "class_type": "Image Comparer (rgthree)",
                "_meta": {"title": "图像比较器"}
            },
            "24": {
                "inputs": {
                    "from_translate": "auto",
                    "to_translate": "en",
                    "manual_translate": false,
                    "Manual Trasnlate": "Manual Trasnlate",
                    "text": "将图中的产品放在一块玻璃展板上，周围都是花朵，有化妆品的广告氛围",
                    "speak_and_recognation": {"__value__": [false, true]}
                },
                "class_type": "GoogleTranslateTextNode",
                "_meta": {"title": "文本翻译(谷歌翻译)"}
            }
        };

        // 日志函数
        function addLog(message) {
            const logSection = document.getElementById('log-section');
            const timestamp = new Date().toLocaleTimeString();
            logSection.textContent += `[${timestamp}] ${message}\n`;
            logSection.scrollTop = logSection.scrollHeight;
        }

        // 更新状态
        function updateStatus(status, progress = 0) {
            document.getElementById('status-text').textContent = status;
            document.getElementById('progress-fill').style.width = progress + '%';
            document.getElementById('progress-text').textContent = Math.round(progress) + '%';
        }

        // 文件上传处理
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewImage = document.getElementById('preview-image');
                    const uploadText = document.getElementById('upload-text');

                    previewImage.src = e.target.result;
                    previewImage.style.display = 'block';
                    uploadText.style.display = 'none';
                };
                reader.readAsDataURL(file);

                // 上传图片到 ComfyUI
                uploadImageToComfyUI(file);
            }
        }

        // 拖拽上传
        const imageUpload = document.getElementById('image-upload');
        imageUpload.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        imageUpload.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        imageUpload.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                document.getElementById('file-input').files = files;
                handleFileSelect({target: {files: [file]}});
            }
        });

        // 上传图片到 ComfyUI
        async function uploadImageToComfyUI(file) {
            try {
                addLog('开始上传图片...');
                updateStatus('上传图片中...', 10);

                const formData = new FormData();
                formData.append('image', file);
                formData.append('overwrite', 'true');

                const response = await fetch(`${COMFYUI_URL}/upload/image`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    uploadedImageName = result.name;
                    addLog(`图片上传成功: ${uploadedImageName}`);
                    updateStatus('图片上传完成', 20);
                } else {
                    throw new Error(`上传失败: ${response.status}`);
                }
            } catch (error) {
                addLog(`图片上传失败: ${error.message}`);
                updateStatus('图片上传失败', 0);
                alert('图片上传失败，请检查网络连接和 ComfyUI 服务状态');
            }
        }

        // 生成图片
        async function generateImage() {
            if (!uploadedImageName) {
                alert('请先上传产品图片！');
                return;
            }

            try {
                const generateBtn = document.getElementById('generate-btn');
                generateBtn.disabled = true;
                generateBtn.textContent = '生成中...';

                addLog('开始生成图片...');
                updateStatus('准备工作流...', 25);

                // 获取参数
                const promptText = document.getElementById('prompt-text').value;
                const steps = parseInt(document.getElementById('steps').value);
                const cfg = parseFloat(document.getElementById('cfg').value);
                const guidance = parseFloat(document.getElementById('guidance').value);
                const seed = parseInt(document.getElementById('seed').value);

                // 创建工作流
                const workflow = JSON.parse(JSON.stringify(workflowTemplate));
                workflow["2"].inputs.seed = seed;
                workflow["2"].inputs.steps = steps;
                workflow["2"].inputs.cfg = cfg;
                workflow["3"].inputs.guidance = guidance;
                workflow["17"].inputs.image = uploadedImageName;
                workflow["24"].inputs.text = promptText;

                addLog('发送工作流到 ComfyUI...');
                updateStatus('发送工作流...', 30);

                // 提交工作流
                const promptResponse = await fetch(`${COMFYUI_URL}/prompt`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: workflow,
                        client_id: 'web_client_' + Date.now()
                    })
                });

                if (!promptResponse.ok) {
                    throw new Error(`工作流提交失败: ${promptResponse.status}`);
                }

                const promptResult = await promptResponse.json();
                currentPromptId = promptResult.prompt_id;

                addLog(`工作流已提交，ID: ${currentPromptId}`);
                updateStatus('工作流执行中...', 40);

                // 监控进度
                monitorProgress(currentPromptId);

            } catch (error) {
                addLog(`生成失败: ${error.message}`);
                updateStatus('生成失败', 0);

                const generateBtn = document.getElementById('generate-btn');
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 生成背景图片';

                alert('生成失败，请检查网络连接和 ComfyUI 服务状态');
            }
        }
        // 监控进度
        async function monitorProgress(promptId) {
            try {
                let completed = false;
                let progress = 40;

                while (!completed) {
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒

                    // 检查队列状态
                    const queueResponse = await fetch(`${COMFYUI_URL}/queue`);
                    const queueData = await queueResponse.json();

                    // 检查是否在运行队列中
                    const isRunning = queueData.queue_running.some(item => item[1] === promptId);
                    const isPending = queueData.queue_pending.some(item => item[1] === promptId);

                    if (isRunning) {
                        progress = Math.min(progress + 2, 80);
                        updateStatus('正在生成图片...', progress);
                        addLog('工作流正在执行中...');
                    } else if (!isPending) {
                        // 不在队列中，可能已完成
                        completed = true;
                        updateStatus('检查生成结果...', 90);
                        addLog('工作流执行完成，获取结果...');

                        // 获取历史记录
                        const historyResponse = await fetch(`${COMFYUI_URL}/history/${promptId}`);
                        const historyData = await historyResponse.json();

                        if (historyData[promptId]) {
                            const outputs = historyData[promptId].outputs;
                            displayResults(outputs);
                            updateStatus('生成完成！', 100);
                            addLog('图片生成成功！');
                        } else {
                            throw new Error('无法获取生成结果');
                        }
                    } else {
                        addLog('工作流在队列中等待...');
                        updateStatus('队列等待中...', progress);
                    }
                }

            } catch (error) {
                addLog(`监控进度失败: ${error.message}`);
                updateStatus('监控失败', 0);
            } finally {
                const generateBtn = document.getElementById('generate-btn');
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 生成背景图片';
            }
        }

        // 显示结果
        function displayResults(outputs) {
            const resultImages = document.getElementById('result-images');
            resultImages.innerHTML = '';

            // 查找预览图像节点的输出
            for (const nodeId in outputs) {
                const nodeOutput = outputs[nodeId];
                if (nodeOutput.images) {
                    nodeOutput.images.forEach(imageInfo => {
                        const imageDiv = document.createElement('div');
                        imageDiv.className = 'result-image';

                        const img = document.createElement('img');
                        img.src = `${COMFYUI_URL}/view?filename=${imageInfo.filename}&type=${imageInfo.type}&subfolder=${imageInfo.subfolder || ''}`;
                        img.alt = '生成的图片';
                        img.onclick = () => downloadImage(img.src, imageInfo.filename);
                        img.style.cursor = 'pointer';
                        img.title = '点击下载图片';

                        imageDiv.appendChild(img);
                        resultImages.appendChild(imageDiv);

                        addLog(`生成图片: ${imageInfo.filename}`);
                    });
                }
            }

            if (resultImages.children.length === 0) {
                resultImages.innerHTML = '<div style="text-align: center; color: #666; padding: 40px;"><p>未找到生成的图片</p></div>';
            }
        }

        // 下载图片
        async function downloadImage(imageUrl, filename) {
            try {
                const response = await fetch(imageUrl);
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = filename || 'generated_image.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                addLog(`下载图片: ${filename}`);
            } catch (error) {
                addLog(`下载失败: ${error.message}`);
                alert('下载失败，请右键保存图片');
            }
        }

        // 随机种子
        function randomizeSeed() {
            const randomSeed = Math.floor(Math.random() * 1000000000000000);
            document.getElementById('seed').value = randomSeed;
            addLog(`生成随机种子: ${randomSeed}`);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('ComfyUI API 控制台已就绪');
            addLog(`连接地址: ${COMFYUI_URL}`);
            updateStatus('等待上传图片...', 0);
        });
    </script>
</body>
</html>
