<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComfyUI 电商产品背景替换工作流</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .workflow-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            border-bottom: 3px solid #4facfe;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #555;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .workflow-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .workflow-info h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .workflow-info ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .workflow-info li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .workflow-info li:last-child {
            border-bottom: none;
        }
        
        .workflow-info li strong {
            color: #4facfe;
        }
        
        .output-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .json-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 ComfyUI 工作流控制台</h1>
            <p>电商产品背景替换 - 智能化图像处理工具</p>
        </div>
        
        <div class="main-content">
            <div class="workflow-info">
                <h3>📋 工作流信息</h3>
                <ul>
                    <li><strong>工作流名称:</strong> 电商产品添加或者更换背景</li>
                    <li><strong>主要功能:</strong> 使用 Flux 模型为产品图片生成新背景</li>
                    <li><strong>支持模型:</strong> Flux.1-Turbo-Alpha, Nunchaku FluxDiT</li>
                    <li><strong>处理步骤:</strong> 图像加载 → 文本编码 → VAE编码 → 采样生成 → VAE解码 → 预览输出</li>
                </ul>
            </div>
            
            <div class="workflow-section">
                <h2 class="section-title">🖼️ 图像设置</h2>
                <div class="form-group">
                    <label for="input-image">输入图像文件名:</label>
                    <input type="text" id="input-image" value="image (10).png" placeholder="请输入图像文件名">
                </div>
            </div>
            
            <div class="workflow-section">
                <h2 class="section-title">📝 提示词设置</h2>
                <div class="form-group">
                    <label for="prompt-text">中文提示词:</label>
                    <textarea id="prompt-text" placeholder="描述您想要的背景效果...">将图中的产品放在一块玻璃展板上，周围都是花朵，有化妆品的广告氛围</textarea>
                </div>
                <div class="form-group">
                    <label for="english-prompt">英文提示词 (自动翻译):</label>
                    <textarea id="english-prompt" readonly>Place the product in the picture on a glass display board, surrounded by flowers, and has a cosmetics advertising atmosphere</textarea>
                </div>
            </div>
            
            <div class="workflow-section">
                <h2 class="section-title">⚙️ 生成参数</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="steps">采样步数:</label>
                        <input type="number" id="steps" value="8" min="1" max="50">
                    </div>
                    <div class="form-group">
                        <label for="cfg">CFG 引导强度:</label>
                        <input type="number" id="cfg" value="1" min="0.1" max="20" step="0.1">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="guidance">Flux 引导:</label>
                        <input type="number" id="guidance" value="2.5" min="0.1" max="10" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="seed">随机种子:</label>
                        <input type="number" id="seed" value="365395080461699">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="sampler">采样器:</label>
                        <select id="sampler">
                            <option value="euler" selected>Euler</option>
                            <option value="euler_ancestral">Euler Ancestral</option>
                            <option value="dpm_2">DPM 2</option>
                            <option value="dpm_2_ancestral">DPM 2 Ancestral</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="scheduler">调度器:</label>
                        <select id="scheduler">
                            <option value="simple" selected>Simple</option>
                            <option value="normal">Normal</option>
                            <option value="karras">Karras</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <button class="btn" onclick="generateWorkflow()">🚀 生成工作流 JSON</button>
                <button class="btn btn-secondary" onclick="randomizeSeed()">🎲 随机种子</button>
                <button class="btn btn-secondary" onclick="resetForm()">🔄 重置参数</button>
            </div>
            
            <div class="output-section">
                <h3>📄 生成的工作流 JSON</h3>
                <div class="json-output" id="json-output">
点击"生成工作流 JSON"按钮来生成配置文件...
                </div>
                <div style="text-align: center; margin-top: 15px;">
                    <button class="btn" onclick="copyToClipboard()">📋 复制到剪贴板</button>
                    <button class="btn btn-secondary" onclick="downloadJSON()">💾 下载 JSON 文件</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 工作流模板
        const workflowTemplate = {
            "2": {
                "inputs": {
                    "seed": 365395080461699,
                    "steps": 8,
                    "cfg": 1,
                    "sampler_name": "euler",
                    "scheduler": "simple",
                    "denoise": 1,
                    "model": ["22", 0],
                    "positive": ["3", 0],
                    "negative": ["7", 0],
                    "latent_image": ["10", 0]
                },
                "class_type": "KSampler",
                "_meta": {"title": "K采样器"}
            },
            "3": {
                "inputs": {
                    "guidance": 2.5,
                    "conditioning": ["8", 0]
                },
                "class_type": "FluxGuidance",
                "_meta": {"title": "Flux引导"}
            },
            "5": {
                "inputs": {
                    "clip_name1": "clip_l.safetensors",
                    "clip_name2": "t5xxl_fp8_e4m3fn.safetensors",
                    "type": "flux",
                    "device": "default"
                },
                "class_type": "DualCLIPLoader",
                "_meta": {"title": "双CLIP加载器"}
            },
            "6": {
                "inputs": {"vae_name": "ae.sft"},
                "class_type": "VAELoader",
                "_meta": {"title": "加载VAE"}
            },
            "7": {
                "inputs": {"conditioning": ["9", 0]},
                "class_type": "ConditioningZeroOut",
                "_meta": {"title": "条件零化"}
            },
            "8": {
                "inputs": {
                    "conditioning": ["9", 0],
                    "latent": ["10", 0]
                },
                "class_type": "ReferenceLatent",
                "_meta": {"title": "ReferenceLatent"}
            },
            "9": {
                "inputs": {
                    "text": ["20", 0],
                    "speak_and_recognation": {"__value__": [false, true]},
                    "clip": ["5", 0]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}
            },
            "10": {
                "inputs": {
                    "pixels": ["18", 0],
                    "vae": ["6", 0]
                },
                "class_type": "VAEEncode",
                "_meta": {"title": "VAE编码"}
            },
            "11": {
                "inputs": {
                    "samples": ["2", 0],
                    "vae": ["6", 0]
                },
                "class_type": "VAEDecode",
                "_meta": {"title": "VAE解码"}
            },
            "17": {
                "inputs": {
                    "image": "image (10).png",
                    "keep_alpha": false,
                    "🔄 refresh from Temp": null,
                    "🔄 refresh from Output": null
                },
                "class_type": "LG_LoadImage",
                "_meta": {"title": "🎈LG_加载图像"}
            },
            "18": {
                "inputs": {"image": ["17", 0]},
                "class_type": "FluxKontextImageScale",
                "_meta": {"title": "FluxKontextImageScale"}
            },
            "19": {
                "inputs": {"images": ["11", 0]},
                "class_type": "PreviewImage",
                "_meta": {"title": "预览图像"}
            },
            "20": {
                "inputs": {
                    "PreviewTextNode_0": "Place the product in the picture on a glass display board, surrounded by flowers, and has a cosmetics advertising atmosphere",
                    "text": ["24", 0]
                },
                "class_type": "PreviewTextNode",
                "_meta": {"title": "文本预览"}
            },
            "21": {
                "inputs": {
                    "model_path": "svdq/svdq-int4_r32-flux.1-kontext-dev.safetensors",
                    "cache_threshold": 0,
                    "attention": "nunchaku-fp16",
                    "cpu_offload": "enable",
                    "device_id": 0,
                    "data_type": "float16",
                    "i2f_mode": "enabled"
                },
                "class_type": "NunchakuFluxDiTLoader",
                "_meta": {"title": "Nunchaku FluxDiT 加载器"}
            },
            "22": {
                "inputs": {
                    "lora_name": "FLUX.1-Turbo-Alpha.safetensors",
                    "lora_strength": 1,
                    "model": ["21", 0]
                },
                "class_type": "NunchakuFluxLoraLoader",
                "_meta": {"title": "Nunchaku FluxLora 加载器"}
            },
            "23": {
                "inputs": {
                    "rgthree_comparer": {
                        "images": [
                            {
                                "name": "A",
                                "selected": true,
                                "url": "/api/view?filename=rgthree.compare._temp_jhjrh_00003_.png&type=temp&subfolder=&preview=jpeg&rand=0.4932305761510609"
                            },
                            {
                                "name": "B",
                                "selected": true,
                                "url": "/api/view?filename=rgthree.compare._temp_jhjrh_00004_.png&type=temp&subfolder=&preview=jpeg&rand=0.6381766265531946"
                            }
                        ]
                    },
                    "image_a": ["11", 0],
                    "image_b": ["18", 0]
                },
                "class_type": "Image Comparer (rgthree)",
                "_meta": {"title": "图像比较器"}
            },
            "24": {
                "inputs": {
                    "from_translate": "auto",
                    "to_translate": "en",
                    "manual_translate": false,
                    "Manual Trasnlate": "Manual Trasnlate",
                    "text": "将图中的产品放在一块玻璃展板上，周围都是花朵，有化妆品的广告氛围",
                    "speak_and_recognation": {"__value__": [false, true]}
                },
                "class_type": "GoogleTranslateTextNode",
                "_meta": {"title": "文本翻译(谷歌翻译)"}
            }
        };

        function generateWorkflow() {
            // 获取表单数据
            const inputImage = document.getElementById('input-image').value;
            const promptText = document.getElementById('prompt-text').value;
            const englishPrompt = document.getElementById('english-prompt').value;
            const steps = parseInt(document.getElementById('steps').value);
            const cfg = parseFloat(document.getElementById('cfg').value);
            const guidance = parseFloat(document.getElementById('guidance').value);
            const seed = parseInt(document.getElementById('seed').value);
            const sampler = document.getElementById('sampler').value;
            const scheduler = document.getElementById('scheduler').value;

            // 创建新的工作流
            const newWorkflow = JSON.parse(JSON.stringify(workflowTemplate));
            
            // 更新参数
            newWorkflow["2"].inputs.seed = seed;
            newWorkflow["2"].inputs.steps = steps;
            newWorkflow["2"].inputs.cfg = cfg;
            newWorkflow["2"].inputs.sampler_name = sampler;
            newWorkflow["2"].inputs.scheduler = scheduler;
            newWorkflow["3"].inputs.guidance = guidance;
            newWorkflow["17"].inputs.image = inputImage;
            newWorkflow["20"].inputs.PreviewTextNode_0 = englishPrompt;
            newWorkflow["24"].inputs.text = promptText;

            // 显示生成的 JSON
            const jsonOutput = document.getElementById('json-output');
            jsonOutput.textContent = JSON.stringify(newWorkflow, null, 2);
        }

        function randomizeSeed() {
            const randomSeed = Math.floor(Math.random() * 1000000000000000);
            document.getElementById('seed').value = randomSeed;
        }

        function resetForm() {
            document.getElementById('input-image').value = 'image (10).png';
            document.getElementById('prompt-text').value = '将图中的产品放在一块玻璃展板上，周围都是花朵，有化妆品的广告氛围';
            document.getElementById('english-prompt').value = 'Place the product in the picture on a glass display board, surrounded by flowers, and has a cosmetics advertising atmosphere';
            document.getElementById('steps').value = 8;
            document.getElementById('cfg').value = 1;
            document.getElementById('guidance').value = 2.5;
            document.getElementById('seed').value = 365395080461699;
            document.getElementById('sampler').value = 'euler';
            document.getElementById('scheduler').value = 'simple';
        }

        function copyToClipboard() {
            const jsonOutput = document.getElementById('json-output');
            navigator.clipboard.writeText(jsonOutput.textContent).then(() => {
                alert('JSON 已复制到剪贴板！');
            });
        }

        function downloadJSON() {
            const jsonOutput = document.getElementById('json-output');
            const blob = new Blob([jsonOutput.textContent], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '电商产品背景替换_' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 监听中文提示词变化，模拟翻译
        document.getElementById('prompt-text').addEventListener('input', function() {
            // 这里可以集成真实的翻译 API
            // 现在只是简单的示例
            const chineseText = this.value;
            // 简单的翻译映射示例
            document.getElementById('english-prompt').value = chineseText;
        });
    </script>
</body>
</html>
