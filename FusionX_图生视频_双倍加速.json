{"2": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "wanvideo", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true, "images": ["14", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "合并为视频"}}, "3": {"inputs": {"blocks_to_swap": 30, "offload_img_emb": false, "offload_txt_emb": false, "use_non_blocking": true, "vace_blocks_to_swap": 0}, "class_type": "WanVideoBlockSwap", "_meta": {"title": "WanVideo 块交换"}}, "5": {"inputs": {"positive_prompt": ["21", 0], "negative_prompt": "Overexposure, static, blurred details, subtitles, paintings, pictures, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, mutilated, redundant fingers, poorly painted hands, poorly painted faces, deformed, disfigured, deformed limbs, fused fingers, cluttered background, three legs, a lot of people in the background, upside down", "force_offload": false, "嵌入提示词": null, "t5": ["23", 0]}, "class_type": "WanVideoTextEncode", "_meta": {"title": "WanVideo T5 文本编码"}}, "9": {"inputs": {"enable_vae_tiling": true, "tile_x": 272, "tile_y": 272, "tile_stride_x": 144, "tile_stride_y": 128, "normalization": "default", "vae": ["22", 0], "samples": ["13", 0]}, "class_type": "WanVideoDecode", "_meta": {"title": "WanVideo 解码"}}, "10": {"inputs": {"method": "mkl", "strength": 1, "image_ref": ["26", 0], "image_target": ["9", 0]}, "class_type": "ColorMatch", "_meta": {"title": "图像调色"}}, "11": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "pl-vace14b", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": false, "images": ["10", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "合并为视频"}}, "12": {"inputs": {"int": 33}, "class_type": "Primitive integer [Crysto<PERSON>]", "_meta": {"title": "总帧数=帧率×秒数+1"}}, "13": {"inputs": {"steps": ["24", 0], "cfg": 1.0000000000000002, "shift": 8.000000000000002, "seed": 29102138285024, "force_offload": true, "scheduler": "unipc", "riflex_freq_index": 0, "denoise_strength": 1, "batched_cfg": false, "rope_function": "comfy", "model": ["17", 0], "image_embeds": ["37", 0], "text_embeds": ["5", 0], "feta_args": ["28", 0], "slg_args": ["27", 0], "experimental_args": ["29", 0], "teacache_args": ["42", 0]}, "class_type": "WanVideoSampler", "_meta": {"title": "WanVideo 采样器"}}, "14": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["26", 0], "image2": ["10", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "图像联结"}}, "17": {"inputs": {"model": "Wan/FusionX/图生视频/FusionX_I2V_14B.safetensors", "base_precision": "fp16", "quantization": "fp8_e4m3fn", "load_device": "offload_device", "attention_mode": "sageattn", "block_swap_args": ["3", 0]}, "class_type": "WanVideoModelLoader", "_meta": {"title": "WanVideo 模型加载器"}}, "20": {"inputs": {"image": "Gemini_Generated_Image_nzst7jnzst7jnzst.png"}, "class_type": "LoadImage", "_meta": {"title": "加载参考图"}}, "21": {"inputs": {"text": "宇宙飞船快速飞过地球仪"}, "class_type": "Text Multiline", "_meta": {"title": "提示词"}}, "22": {"inputs": {"model_name": "Wan2.1_VAE_bf16.safetensors", "precision": "bf16"}, "class_type": "WanVideoVAELoader", "_meta": {"title": "WanVideo VAE 加载器"}}, "23": {"inputs": {"model_name": "<PERSON><PERSON><PERSON><PERSON>封装/umt5_xxl_enc_fp8_e4m3fn.safetensors", "precision": "bf16", "load_device": "offload_device", "quantization": "fp8_e4m3fn"}, "class_type": "LoadWanVideoT5TextEncoder", "_meta": {"title": "加载 WanVideo T5 文本编码器"}}, "24": {"inputs": {"int": 10}, "class_type": "Primitive integer [Crysto<PERSON>]", "_meta": {"title": "步数"}}, "26": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": ["34", 0], "background_color": "#000000", "image": ["20", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "图层工具：按宽高比缩放 V2"}}, "27": {"inputs": {"blocks": "9", "start_percent": 0.20000000000000004, "end_percent": 0.7000000000000002}, "class_type": "WanVideoSLG", "_meta": {"title": "WanVideo SLG"}}, "28": {"inputs": {"weight": 2, "start_percent": 0, "end_percent": 1}, "class_type": "WanVideoEnhanceAVideo", "_meta": {"title": "WanVideo 视频增强"}}, "29": {"inputs": {"video_attention_split_steps": "", "cfg_zero_star": true, "use_zero_init": false, "zero_star_steps": 0, "use_fresca": false, "fresca_scale_low": 1, "fresca_scale_high": 1.25, "fresca_freq_cutoff": 20}, "class_type": "WanVideoExperimentalArgs", "_meta": {"title": "WanVideo 实验参数"}}, "34": {"inputs": {"value": 512}, "class_type": "JWInteger", "_meta": {"title": "分辨率最长边"}}, "37": {"inputs": {"width": ["26", 3], "height": ["26", 4], "num_frames": ["12", 0], "noise_aug_strength": 0, "start_latent_strength": 1, "end_latent_strength": 1, "force_offload": true, "fun_or_fl2v_model": true, "tiled_vae": false, "vae": ["22", 0], "clip_embeds": ["38", 0], "start_image": ["26", 0]}, "class_type": "WanVideoImageToVideoEncode", "_meta": {"title": "WanVideo 图像转视频编码"}}, "38": {"inputs": {"strength_1": 1, "strength_2": 1, "crop": "center", "combine_embeds": "average", "force_offload": true, "tiles": 0, "ratio": 0.5, "clip_vision": ["39", 0], "image_1": ["26", 0]}, "class_type": "WanVideoClipVisionEncode", "_meta": {"title": "WanVideo 视觉编码器编码"}}, "39": {"inputs": {"clip_name": "Wan_官方原生/clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "加载CLIP视觉"}}, "40": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "pl-vace14b", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "合并为视频"}}, "41": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "wanvideo", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "合并为视频"}}, "42": {"inputs": {"rel_l1_thresh": 0.15000000000000002, "start_step": 2, "end_step": -1, "cache_device": "offload_device", "use_coefficients": true, "mode": "e"}, "class_type": "WanVideoTeaCache", "_meta": {"title": "WanVideo Tea缓存"}}}