{"2": {"inputs": {"seed": 365395080461699, "steps": 8, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["22", 0], "positive": ["3", 0], "negative": ["7", 0], "latent_image": ["10", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "3": {"inputs": {"guidance": 2.5, "conditioning": ["8", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "5": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "6": {"inputs": {"vae_name": "ae.sft"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "7": {"inputs": {"conditioning": ["9", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "8": {"inputs": {"conditioning": ["9", 0], "latent": ["10", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "9": {"inputs": {"text": ["20", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["5", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "10": {"inputs": {"pixels": ["18", 0], "vae": ["6", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "11": {"inputs": {"samples": ["2", 0], "vae": ["6", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "17": {"inputs": {"image": "image (10).png", "keep_alpha": false, "🔄 refresh from Temp": null, "🔄 refresh from Output": null}, "class_type": "LG_LoadImage", "_meta": {"title": "🎈LG_加载图像"}}, "18": {"inputs": {"image": ["17", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "19": {"inputs": {"images": ["11", 0]}, "class_type": "PreviewImage", "_meta": {"title": "预览图像"}}, "20": {"inputs": {"PreviewTextNode_0": "Place the product in the picture on a glass display board, surrounded by flowers, and has a cosmetics advertising atmosphere", "text": ["24", 0]}, "class_type": "PreviewTextNode", "_meta": {"title": "文本预览"}}, "21": {"inputs": {"model_path": "svdq/svdq-int4_r32-flux.1-kontext-dev.safetensors", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "enable", "device_id": 0, "data_type": "float16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FluxDiT 加载器"}}, "22": {"inputs": {"lora_name": "FLUX.1-Turbo-Alpha.safetensors", "lora_strength": 1, "model": ["21", 0]}, "class_type": "NunchakuFluxLoraLoader", "_meta": {"title": "Nunchaku FluxLora 加载器"}}, "23": {"inputs": {"rgthree_comparer": {"images": [{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_jhjrh_00003_.png&type=temp&subfolder=&preview=jpeg&rand=0.4932305761510609"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_jhjrh_00004_.png&type=temp&subfolder=&preview=jpeg&rand=0.6381766265531946"}]}, "image_a": ["11", 0], "image_b": ["18", 0]}, "class_type": "Image Comparer (rgthree)", "_meta": {"title": "图像比较器"}}, "24": {"inputs": {"from_translate": "auto", "to_translate": "en", "manual_translate": false, "Manual Trasnlate": "Manual Trasnlate", "text": "将图中的产品放在一块玻璃展板上，周围都是花朵，有化妆品的广告氛围", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "GoogleTranslateTextNode", "_meta": {"title": "文本翻译(谷歌翻译)"}}}