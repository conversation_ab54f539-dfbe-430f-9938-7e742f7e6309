# 通用 ComfyUI 工作流管理系统 使用说明

## 🎯 系统概述

这是一个通用的 ComfyUI 工作流管理系统，能够：

- ✅ **多服务器支持** - 自定义连接任意 ComfyUI 服务器
- ✅ **智能工作流解析** - 自动识别 JSON 工作流的文件需求
- ✅ **多文件类型支持** - 图片、视频、混合文件上传
- ✅ **参数动态调整** - 自动生成参数调整界面
- ✅ **实时进度监控** - 完整的执行状态跟踪
- ✅ **结果自动展示** - 支持图片和视频结果预览下载

## 🚀 快速开始

### 1. 配置服务器连接

1. 在左侧面板输入您的 ComfyUI 服务器地址
   ```
   示例: https://your-server.com:8188
   ```

2. 点击 "🔗 测试连接" 按钮
3. 确保显示 "✅ 已连接" 状态

### 2. 上传工作流文件

1. 点击 "📁 点击上传 .json 工作流文件"
2. 选择从 ComfyUI 导出的 API 格式 JSON 文件
3. 系统会自动解析工作流并显示：
   - 节点总数和类型
   - 需要上传的文件类型和数量
   - 可调整的参数

### 3. 上传所需文件

根据系统提示上传相应的文件：

- **图片文件**: 支持 JPG, PNG, WebP 等格式
- **视频文件**: 支持 MP4, AVI, MOV 等格式
- **多文件支持**: 可以为同一个节点上传多个文件

上传方式：
- 点击上传区域选择文件
- 直接拖拽文件到上传区域

### 4. 调整参数（可选）

系统会自动识别工作流中的可调参数，如：
- 采样步数
- CFG 强度
- 种子值
- 文本提示词
- 其他数值参数

### 5. 开始生成

1. 确保所有必需文件已上传
2. 点击 "🚀 开始生成" 按钮
3. 系统会自动：
   - 上传文件到服务器
   - 更新工作流参数
   - 提交执行任务
   - 监控执行进度
   - 展示生成结果

## 📋 支持的工作流类型

### 图像处理工作流
- **背景替换**: 产品图片背景更换
- **风格转换**: 图片风格化处理
- **图像增强**: 超分辨率、去噪等
- **图像生成**: 文本到图像生成

### 视频处理工作流
- **视频生成**: 图片到视频转换
- **视频编辑**: 视频风格化、特效
- **动画制作**: 角色动画生成

### 混合工作流
- **图片+视频**: 同时处理图片和视频
- **多模态生成**: 结合文本、图片、视频的复杂工作流

## 🔧 技术特性

### 智能文件识别

系统通过以下方式识别文件需求：

1. **节点类型识别**:
   ```
   图片节点: LoadImage, LG_LoadImage, LoadImageMask
   视频节点: VHS_LoadVideo, LoadVideo, VideoLoad
   ```

2. **参数名称识别**:
   ```
   图片参数: image, input_image, source_image
   视频参数: video, input_video, source_video
   ```

### 参数自动识别

系统会自动识别以下类型的参数：
- **数值参数**: steps, cfg, seed, strength 等
- **文本参数**: prompt, negative_prompt 等
- **选择参数**: sampler_name, scheduler 等

### 多服务器管理

支持连接不同的 ComfyUI 服务器：
- 本地服务器
- 云端服务器
- 集群服务器

## ⚠️ 注意事项

### CORS 配置

确保您的 ComfyUI 服务器支持跨域访问：

```bash
# 启动 ComfyUI 时添加 CORS 参数
python main.py --enable-cors-header
```

### 文件大小限制

- **图片文件**: 建议不超过 50MB
- **视频文件**: 建议不超过 500MB
- **总文件数**: 建议不超过 20 个

### 网络要求

- 稳定的网络连接
- 足够的上传带宽
- 服务器响应时间 < 30秒

## 🐛 故障排除

### 连接问题

**问题**: 显示 "❌ 未连接"
**解决方案**:
1. 检查服务器地址是否正确
2. 确认 ComfyUI 正在运行
3. 检查防火墙设置
4. 添加 CORS 参数重启服务器

### 工作流解析失败

**问题**: "工作流解析失败"
**解决方案**:
1. 确认 JSON 文件格式正确
2. 使用 ComfyUI 的 "Save (API Format)" 导出
3. 检查文件是否损坏

### 文件上传失败

**问题**: 文件上传失败
**解决方案**:
1. 检查文件格式是否支持
2. 确认文件大小在限制范围内
3. 检查网络连接稳定性
4. 重试上传操作

### 生成失败

**问题**: 工作流执行失败
**解决方案**:
1. 检查服务器资源是否充足
2. 确认所需模型已加载
3. 查看服务器日志错误信息
4. 检查工作流参数是否合理

## 📞 技术支持

### 日志查看

系统提供详细的执行日志：
- 连接状态日志
- 文件上传日志
- 工作流执行日志
- 错误信息日志

### 调试模式

打开浏览器开发者工具查看：
- 网络请求详情
- JavaScript 错误信息
- API 响应内容

### 常用检查点

1. **服务器连接**: 绿色 "✅ 已连接" 状态
2. **工作流加载**: 显示节点信息和文件需求
3. **文件上传**: 预览区域显示已上传文件
4. **参数设置**: 参数区域显示可调整选项
5. **生成按钮**: 按钮可点击且显示正确文本

## 🔄 更新日志

- **v1.0**: 基础功能实现
- **v1.1**: 添加多文件支持
- **v1.2**: 改进错误处理和用户体验
- **v1.3**: 增加视频文件支持
- **v1.4**: 优化参数识别和界面布局

## 💡 使用技巧

1. **批量处理**: 可以为同一节点上传多个文件进行批量处理
2. **参数保存**: 浏览器会记住您的服务器地址设置
3. **快速重试**: 生成失败后可以直接重新点击生成按钮
4. **结果下载**: 点击生成的图片或视频即可下载
5. **工作流复用**: 同一个工作流可以重复使用，只需更换文件
