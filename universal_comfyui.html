<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通用 ComfyUI 工作流管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
        }
        
        .panel {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }
        
        .panel.active {
            border-color: #4facfe;
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.2);
        }
        
        .section-title {
            font-size: 1.6em;
            color: #333;
            margin-bottom: 20px;
            border-bottom: 3px solid #4facfe;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #555;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            margin: 5px;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(79, 172, 254, 0.3);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .btn-small {
            padding: 8px 16px;
            font-size: 12px;
            width: auto;
        }
        
        .file-upload {
            border: 2px dashed #4facfe;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-bottom: 15px;
        }
        
        .file-upload:hover {
            background-color: #f0f8ff;
        }
        
        .file-upload.dragover {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        
        .file-upload input {
            display: none;
        }
        
        .file-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .file-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: #f5f5f5;
            border: 2px solid #ddd;
        }
        
        .file-item img, .file-item video {
            width: 100%;
            height: 100px;
            object-fit: cover;
        }
        
        .file-item .file-name {
            padding: 8px;
            font-size: 12px;
            background: rgba(0,0,0,0.7);
            color: white;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        
        .file-item .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .status-section {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 5px solid #2196f3;
        }
        
        .status-text {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 8px;
        }
        
        .progress-bar {
            width: 100%;
            height: 15px;
            background: #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .workflow-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .workflow-info h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .workflow-info ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .workflow-info li {
            color: #856404;
            margin-bottom: 5px;
        }
        
        .log-section {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .result-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .result-image {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
        }
        
        .result-image img, .result-image video {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .connection-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .connection-status.connected {
            background: #d4edda;
            color: #155724;
        }
        
        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .connection-status.testing {
            background: #fff3cd;
            color: #856404;
        }
        
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 通用 ComfyUI 工作流管理系统</h1>
            <p>支持多服务器、多工作流、多文件类型的智能化管理平台</p>
        </div>
        
        <div class="main-content">
            <!-- 左侧面板 - 服务器和工作流配置 -->
            <div class="panel" id="config-panel">
                <h2 class="section-title">⚙️ 配置管理</h2>
                
                <!-- 服务器配置 -->
                <div class="form-group">
                    <label for="server-url">ComfyUI 服务器地址:</label>
                    <input type="text" id="server-url" placeholder="https://your-comfyui-server.com:8188" 
                           value="https://8ee9184910a741fb9bd9bb258301313a--8188.ap-shanghai.cloudstudio.club">
                    <div style="margin-top: 8px;">
                        <span class="connection-status disconnected" id="connection-status">未连接</span>
                    </div>
                </div>
                
                <button class="btn btn-secondary" onclick="testConnection()">🔗 测试连接</button>
                
                <!-- 工作流上传 -->
                <div class="form-group" style="margin-top: 25px;">
                    <label for="workflow-file">上传工作流 JSON:</label>
                    <div class="file-upload" onclick="document.getElementById('workflow-file').click()">
                        <input type="file" id="workflow-file" accept=".json" onchange="handleWorkflowUpload(event)">
                        <p>📁 点击上传 .json 工作流文件</p>
                        <p style="font-size: 12px; color: #666; margin-top: 5px;">支持从 ComfyUI 导出的 API 格式</p>
                    </div>
                </div>
                
                <!-- 工作流信息 -->
                <div id="workflow-info" style="display: none;">
                    <div class="workflow-info">
                        <h4>📋 工作流信息</h4>
                        <ul id="workflow-details"></ul>
                    </div>
                    
                    <button class="btn btn-danger btn-small" onclick="clearWorkflow()">清除工作流</button>
                </div>
                
                <!-- 参数配置 -->
                <div id="parameters-section" style="display: none;">
                    <h3 style="margin: 20px 0 15px 0; color: #333;">🎛️ 参数设置</h3>
                    <div id="parameters-container"></div>
                </div>
            </div>
            
            <!-- 中间面板 - 文件上传 -->
            <div class="panel" id="upload-panel">
                <h2 class="section-title">📁 文件管理</h2>
                
                <div id="file-requirements" style="display: none;">
                    <div class="workflow-info">
                        <h4>📋 需要上传的文件</h4>
                        <ul id="file-requirements-list"></ul>
                    </div>
                </div>
                
                <div id="upload-sections"></div>

                <!-- 提示词设置区域 -->
                <div id="prompt-section" style="display: none; margin-top: 20px;">
                    <h3 style="margin-bottom: 15px; color: #333;">💬 提示词设置</h3>
                    <div id="prompt-container"></div>
                </div>

                <div style="margin-top: 20px;">
                    <button class="btn" id="generate-btn" onclick="generateContent()" disabled>🚀 开始生成</button>
                    <button class="btn btn-secondary" onclick="clearAllFiles()">🗑️ 清除所有文件</button>
                </div>
            </div>
            
            <!-- 右侧面板 - 状态和结果 -->
            <div class="panel" id="result-panel">
                <h2 class="section-title">📊 执行状态</h2>
                
                <div class="status-section">
                    <div class="status-text" id="status-text">等待配置...</div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div id="progress-text" style="font-size: 12px; color: #666; margin-top: 5px;">0%</div>
                </div>
                
                <h3 style="margin: 20px 0 15px 0; color: #333;">🖼️ 生成结果</h3>
                <div class="result-images" id="result-images">
                    <div style="text-align: center; color: #666; padding: 30px;">
                        <p>生成的内容将在这里显示</p>
                    </div>
                </div>
                
                <h3 style="margin: 20px 0 15px 0; color: #333;">📋 执行日志</h3>
                <div class="log-section" id="log-section">
系统已就绪，等待操作...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentServerUrl = '';
        let currentWorkflow = null;
        let uploadedFiles = {};
        let currentPromptId = null;
        let fileRequirements = [];
        
        // 日志函数
        function addLog(message) {
            const logSection = document.getElementById('log-section');
            const timestamp = new Date().toLocaleTimeString();
            logSection.textContent += `[${timestamp}] ${message}\n`;
            logSection.scrollTop = logSection.scrollHeight;
        }
        
        // 更新状态
        function updateStatus(status, progress = 0) {
            document.getElementById('status-text').textContent = status;
            document.getElementById('progress-fill').style.width = progress + '%';
            document.getElementById('progress-text').textContent = Math.round(progress) + '%';
        }
        
        // 更新连接状态
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connection-status');
            statusElement.className = `connection-status ${status}`;

            switch(status) {
                case 'connected':
                    statusElement.textContent = '✅ 已连接';
                    break;
                case 'disconnected':
                    statusElement.textContent = '❌ 未连接';
                    break;
                case 'testing':
                    statusElement.textContent = '🔄 测试中...';
                    break;
            }
        }

        // 测试服务器连接
        async function testConnection() {
            const serverUrl = document.getElementById('server-url').value.trim();
            if (!serverUrl) {
                alert('请输入服务器地址');
                return;
            }

            updateConnectionStatus('testing');
            addLog(`测试连接: ${serverUrl}`);

            try {
                const response = await fetch(`${serverUrl}/queue`, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                if (response.ok) {
                    currentServerUrl = serverUrl;
                    updateConnectionStatus('connected');
                    addLog('✅ 服务器连接成功');
                    updateStatus('服务器连接成功', 10);
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateConnectionStatus('disconnected');
                addLog(`❌ 连接失败: ${error.message}`);
                updateStatus('连接失败', 0);

                if (error.message.includes('Failed to fetch')) {
                    alert('连接失败！请检查：\n1. 服务器地址是否正确\n2. ComfyUI 是否正在运行\n3. 是否添加了 --enable-cors-header 参数');
                }
                return false;
            }
        }

        // 处理工作流文件上传
        function handleWorkflowUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            addLog(`上传工作流文件: ${file.name}`);

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const workflow = JSON.parse(e.target.result);
                    currentWorkflow = workflow;
                    analyzeWorkflow(workflow);
                    addLog('✅ 工作流解析成功');
                } catch (error) {
                    addLog(`❌ 工作流解析失败: ${error.message}`);
                    alert('工作流文件格式错误，请确保是有效的 JSON 文件');
                }
            };
            reader.readAsText(file);
        }

        // 分析工作流，识别需要的文件类型
        function analyzeWorkflow(workflow) {
            fileRequirements = [];
            const nodeTypes = {};
            let parameterNodes = [];
            let promptNodes = [];

            // 分析每个节点
            for (const nodeId in workflow) {
                const node = workflow[nodeId];
                const classType = node.class_type;
                const inputs = node.inputs || {};

                // 记录节点类型
                if (!nodeTypes[classType]) {
                    nodeTypes[classType] = [];
                }
                nodeTypes[classType].push(nodeId);

                // 识别文件输入节点
                if (isImageInputNode(classType, inputs)) {
                    fileRequirements.push({
                        nodeId: nodeId,
                        type: 'image',
                        name: node._meta?.title || `图片输入 (${nodeId})`,
                        inputKey: getImageInputKey(classType, inputs),
                        required: true
                    });
                }

                if (isVideoInputNode(classType, inputs)) {
                    fileRequirements.push({
                        nodeId: nodeId,
                        type: 'video',
                        name: node._meta?.title || `视频输入 (${nodeId})`,
                        inputKey: getVideoInputKey(classType, inputs),
                        required: true
                    });
                }

                // 识别提示词节点
                if (isPromptNode(classType, inputs)) {
                    promptNodes.push({
                        nodeId: nodeId,
                        classType: classType,
                        inputs: inputs,
                        title: node._meta?.title || classType
                    });
                }

                // 识别其他可调参数
                if (hasAdjustableParameters(classType, inputs)) {
                    parameterNodes.push({
                        nodeId: nodeId,
                        classType: classType,
                        inputs: inputs,
                        title: node._meta?.title || classType
                    });
                }
            }

            // 显示工作流信息
            displayWorkflowInfo(nodeTypes, fileRequirements.length);

            // 创建文件上传界面
            createFileUploadSections();

            // 创建提示词界面
            createPromptSections(promptNodes);

            // 创建参数调整界面
            createParameterSections(parameterNodes);

            // 更新状态
            updateStatus('工作流已加载，请上传所需文件', 20);
            updateGenerateButton();
        }

        // 识别图片输入节点
        function isImageInputNode(classType, inputs) {
            const imageNodeTypes = [
                'LoadImage', 'LG_LoadImage', 'LoadImageMask', 'ImageLoad',
                'VHS_LoadVideo', 'LoadImageFromUrl', 'LoadImageBatch'
            ];

            // 检查类型名称
            if (imageNodeTypes.some(type => classType.includes(type))) {
                return true;
            }

            // 检查输入参数
            for (const key in inputs) {
                if (key.toLowerCase().includes('image') && typeof inputs[key] === 'string') {
                    return true;
                }
            }

            return false;
        }

        // 识别视频输入节点
        function isVideoInputNode(classType, inputs) {
            const videoNodeTypes = [
                'VHS_LoadVideo', 'LoadVideo', 'VideoLoad', 'LoadVideoPath'
            ];

            // 检查类型名称
            if (videoNodeTypes.some(type => classType.includes(type))) {
                return true;
            }

            // 检查输入参数
            for (const key in inputs) {
                if (key.toLowerCase().includes('video') && typeof inputs[key] === 'string') {
                    return true;
                }
            }

            return false;
        }

        // 获取图片输入键名
        function getImageInputKey(classType, inputs) {
            const commonKeys = ['image', 'input_image', 'source_image', 'img'];

            for (const key of commonKeys) {
                if (inputs.hasOwnProperty(key)) {
                    return key;
                }
            }

            // 查找包含 image 的键
            for (const key in inputs) {
                if (key.toLowerCase().includes('image') && typeof inputs[key] === 'string') {
                    return key;
                }
            }

            return 'image'; // 默认
        }

        // 获取视频输入键名
        function getVideoInputKey(classType, inputs) {
            const commonKeys = ['video', 'input_video', 'source_video', 'vid'];

            for (const key of commonKeys) {
                if (inputs.hasOwnProperty(key)) {
                    return key;
                }
            }

            // 查找包含 video 的键
            for (const key in inputs) {
                if (key.toLowerCase().includes('video') && typeof inputs[key] === 'string') {
                    return key;
                }
            }

            return 'video'; // 默认
        }

        // 识别提示词节点
        function isPromptNode(classType, inputs) {
            const promptNodeTypes = [
                'CLIPTextEncode', 'CLIPTextEncodeSDXL', 'PromptNode', 'TextNode',
                'PreviewTextNode', 'GoogleTranslateTextNode', 'StringFunction'
            ];

            // 检查类型名称
            if (promptNodeTypes.some(type => classType.includes(type))) {
                return true;
            }

            // 检查输入参数
            for (const key in inputs) {
                const keyLower = key.toLowerCase();
                if ((keyLower.includes('text') || keyLower.includes('prompt')) &&
                    typeof inputs[key] === 'string' &&
                    !keyLower.includes('path') &&
                    !keyLower.includes('file')) {
                    return true;
                }
            }

            return false;
        }

        // 检查是否有可调参数（排除提示词参数）
        function hasAdjustableParameters(classType, inputs) {
            const adjustableTypes = ['number', 'string'];

            for (const key in inputs) {
                const value = inputs[key];
                const keyLower = key.toLowerCase();

                if (adjustableTypes.includes(typeof value) && !Array.isArray(value)) {
                    // 排除文件路径参数和提示词参数
                    if (!keyLower.includes('image') &&
                        !keyLower.includes('video') &&
                        !keyLower.includes('path') &&
                        !keyLower.includes('text') &&
                        !keyLower.includes('prompt')) {
                        return true;
                    }
                }
            }

            return false;
        }

        // 显示工作流信息
        function displayWorkflowInfo(nodeTypes, fileCount) {
            const workflowInfo = document.getElementById('workflow-info');
            const workflowDetails = document.getElementById('workflow-details');

            workflowInfo.style.display = 'block';

            let html = '';
            html += `<li><strong>节点总数:</strong> ${Object.keys(currentWorkflow).length}</li>`;
            html += `<li><strong>需要文件:</strong> ${fileCount} 个</li>`;
            html += `<li><strong>主要节点类型:</strong></li>`;

            for (const type in nodeTypes) {
                if (nodeTypes[type].length > 0) {
                    html += `<li style="margin-left: 20px;">• ${type} (${nodeTypes[type].length})</li>`;
                }
            }

            workflowDetails.innerHTML = html;
        }

        // 创建文件上传区域
        function createFileUploadSections() {
            const uploadSections = document.getElementById('upload-sections');
            const fileRequirementsList = document.getElementById('file-requirements-list');
            const fileRequirementsDiv = document.getElementById('file-requirements');

            if (fileRequirements.length === 0) {
                uploadSections.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">此工作流不需要上传文件</p>';
                fileRequirementsDiv.style.display = 'none';
                return;
            }

            // 显示文件需求
            fileRequirementsDiv.style.display = 'block';
            let requirementsHtml = '';
            fileRequirements.forEach(req => {
                const icon = req.type === 'image' ? '🖼️' : '🎬';
                requirementsHtml += `<li>${icon} ${req.name} (${req.type})</li>`;
            });
            fileRequirementsList.innerHTML = requirementsHtml;

            // 创建上传区域
            let sectionsHtml = '';
            fileRequirements.forEach((req, index) => {
                const icon = req.type === 'image' ? '🖼️' : '🎬';
                const accept = req.type === 'image' ? 'image/*' : 'video/*';

                sectionsHtml += `
                    <div class="form-group">
                        <label>${icon} ${req.name}</label>
                        <div class="file-upload" onclick="document.getElementById('file-${index}').click()"
                             ondrop="handleFileDrop(event, ${index})"
                             ondragover="handleDragOver(event)"
                             ondragleave="handleDragLeave(event)">
                            <input type="file" id="file-${index}" accept="${accept}"
                                   onchange="handleFileSelect(event, ${index})" multiple>
                            <p>📁 点击或拖拽上传 ${req.type === 'image' ? '图片' : '视频'}</p>
                            <p style="font-size: 12px; color: #666; margin-top: 5px;">
                                ${req.type === 'image' ? '支持 JPG, PNG, WebP 等格式' : '支持 MP4, AVI, MOV 等格式'}
                            </p>
                        </div>
                        <div class="file-preview" id="preview-${index}"></div>
                    </div>
                `;
            });

            uploadSections.innerHTML = sectionsHtml;
        }

        // 创建提示词设置区域
        function createPromptSections(promptNodes) {
            const promptSection = document.getElementById('prompt-section');
            const promptContainer = document.getElementById('prompt-container');

            if (promptNodes.length === 0) {
                promptSection.style.display = 'none';
                return;
            }

            promptSection.style.display = 'block';

            let html = '';
            promptNodes.forEach(node => {
                html += `<div style="margin-bottom: 20px; padding: 15px; border: 2px solid #4facfe; border-radius: 10px; background: #f0f8ff;">`;
                html += `<h4 style="margin-bottom: 15px; color: #1976d2; display: flex; align-items: center; gap: 8px;">`;
                html += `<span>💬</span> ${node.title}</h4>`;

                for (const key in node.inputs) {
                    const value = node.inputs[key];
                    const keyLower = key.toLowerCase();

                    // 只处理文本类型的输入
                    if (typeof value === 'string' &&
                        (keyLower.includes('text') || keyLower.includes('prompt')) &&
                        !keyLower.includes('path') &&
                        !keyLower.includes('file')) {

                        const isLongText = value.length > 50 || value.includes('\n');
                        const displayName = key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').trim();

                        html += `
                            <div style="margin-bottom: 15px;">
                                <label style="font-weight: bold; color: #333; margin-bottom: 8px; display: block;">
                                    ${displayName}:
                                </label>
                                ${isLongText ?
                                    `<textarea id="prompt-${node.nodeId}-${key}"
                                             style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px;
                                                    font-size: 14px; min-height: 100px; resize: vertical; font-family: inherit;"
                                             placeholder="请输入${displayName}...">${value}</textarea>` :
                                    `<input type="text" id="prompt-${node.nodeId}-${key}" value="${value}"
                                           style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px;
                                                  font-size: 14px;"
                                           placeholder="请输入${displayName}...">`
                                }
                            </div>
                        `;
                    }
                }

                html += `</div>`;
            });

            promptContainer.innerHTML = html;
        }

        // 创建参数调整区域
        function createParameterSections(parameterNodes) {
            const parametersSection = document.getElementById('parameters-section');
            const parametersContainer = document.getElementById('parameters-container');

            if (parameterNodes.length === 0) {
                parametersSection.style.display = 'none';
                return;
            }

            parametersSection.style.display = 'block';

            let html = '';
            parameterNodes.forEach(node => {
                html += `<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">`;
                html += `<h4 style="margin-bottom: 10px; color: #333;">${node.title}</h4>`;

                for (const key in node.inputs) {
                    const value = node.inputs[key];
                    if (typeof value === 'number') {
                        html += `
                            <div style="margin-bottom: 10px;">
                                <label style="font-size: 12px; color: #666;">${key}:</label>
                                <input type="number" id="param-${node.nodeId}-${key}" value="${value}"
                                       style="width: 100%; padding: 5px; font-size: 12px;">
                            </div>
                        `;
                    } else if (typeof value === 'string' && !key.toLowerCase().includes('image') &&
                               !key.toLowerCase().includes('video') && !key.toLowerCase().includes('path')) {
                        html += `
                            <div style="margin-bottom: 10px;">
                                <label style="font-size: 12px; color: #666;">${key}:</label>
                                <input type="text" id="param-${node.nodeId}-${key}" value="${value}"
                                       style="width: 100%; padding: 5px; font-size: 12px;">
                            </div>
                        `;
                    }
                }

                html += `</div>`;
            });

            parametersContainer.innerHTML = html;
        }

        // 文件拖拽处理
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
        }

        function handleFileDrop(event, index) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect({target: {files: files}}, index);
            }
        }

        // 文件选择处理
        function handleFileSelect(event, index) {
            const files = Array.from(event.target.files);
            if (files.length === 0) return;

            const requirement = fileRequirements[index];
            if (!uploadedFiles[index]) {
                uploadedFiles[index] = [];
            }

            files.forEach(file => {
                // 验证文件类型
                if (!validateFileType(file, requirement.type)) {
                    alert(`文件 ${file.name} 类型不匹配，需要 ${requirement.type} 类型文件`);
                    return;
                }

                uploadedFiles[index].push({
                    file: file,
                    uploaded: false,
                    serverName: null
                });

                addLog(`添加文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
            });

            updateFilePreview(index);
            updateGenerateButton();
        }

        // 验证文件类型
        function validateFileType(file, expectedType) {
            if (expectedType === 'image') {
                return file.type.startsWith('image/');
            } else if (expectedType === 'video') {
                return file.type.startsWith('video/');
            }
            return false;
        }

        // 更新文件预览
        function updateFilePreview(index) {
            const preview = document.getElementById(`preview-${index}`);
            const files = uploadedFiles[index] || [];

            let html = '';
            files.forEach((fileObj, fileIndex) => {
                const file = fileObj.file;
                const isImage = file.type.startsWith('image/');

                html += `
                    <div class="file-item">
                        ${isImage ?
                            `<img src="${URL.createObjectURL(file)}" alt="${file.name}">` :
                            `<video src="${URL.createObjectURL(file)}" controls></video>`
                        }
                        <div class="file-name">${file.name}</div>
                        <button class="remove-btn" onclick="removeFile(${index}, ${fileIndex})">×</button>
                    </div>
                `;
            });

            preview.innerHTML = html;
        }

        // 移除文件
        function removeFile(reqIndex, fileIndex) {
            if (uploadedFiles[reqIndex]) {
                uploadedFiles[reqIndex].splice(fileIndex, 1);
                updateFilePreview(reqIndex);
                updateGenerateButton();
                addLog(`移除文件`);
            }
        }

        // 清除所有文件
        function clearAllFiles() {
            uploadedFiles = {};
            fileRequirements.forEach((req, index) => {
                updateFilePreview(index);
            });
            updateGenerateButton();
            addLog('清除所有文件');
        }

        // 清除工作流
        function clearWorkflow() {
            currentWorkflow = null;
            fileRequirements = [];
            uploadedFiles = {};

            document.getElementById('workflow-info').style.display = 'none';
            document.getElementById('parameters-section').style.display = 'none';
            document.getElementById('prompt-section').style.display = 'none';
            document.getElementById('file-requirements').style.display = 'none';
            document.getElementById('upload-sections').innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">请先上传工作流文件</p>';

            updateGenerateButton();
            updateStatus('请上传工作流文件', 0);
            addLog('工作流已清除');
        }

        // 更新生成按钮状态
        function updateGenerateButton() {
            const generateBtn = document.getElementById('generate-btn');

            if (!currentWorkflow) {
                generateBtn.disabled = true;
                generateBtn.textContent = '🚀 请先上传工作流';
                return;
            }

            if (!currentServerUrl) {
                generateBtn.disabled = true;
                generateBtn.textContent = '🚀 请先连接服务器';
                return;
            }

            // 检查是否所有必需文件都已上传
            let allFilesReady = true;
            fileRequirements.forEach((req, index) => {
                if (req.required && (!uploadedFiles[index] || uploadedFiles[index].length === 0)) {
                    allFilesReady = false;
                }
            });

            if (!allFilesReady) {
                generateBtn.disabled = true;
                generateBtn.textContent = '🚀 请上传所需文件';
                return;
            }

            generateBtn.disabled = false;
            generateBtn.textContent = '🚀 开始生成';
        }

        // 上传文件到服务器
        async function uploadFileToServer(file, type = 'image') {
            try {
                const formData = new FormData();

                // 根据文件类型选择正确的字段名和端点
                if (type === 'video') {
                    formData.append('video', file);
                    formData.append('overwrite', 'true');
                } else {
                    formData.append('image', file);
                    formData.append('overwrite', 'true');
                }

                // 尝试不同的上传端点
                const endpoints = type === 'video' ?
                    ['/upload/video', '/upload/image'] :
                    ['/upload/image'];

                let lastError = null;

                for (const endpoint of endpoints) {
                    try {
                        addLog(`尝试上传到端点: ${endpoint}`);

                        const response = await fetch(`${currentServerUrl}${endpoint}`, {
                            method: 'POST',
                            mode: 'cors',
                            body: formData
                        });

                        if (response.ok) {
                            const result = await response.json();
                            addLog(`✅ 上传成功，服务器返回: ${JSON.stringify(result)}`);
                            return result.name || result.filename;
                        } else {
                            const errorText = await response.text();
                            lastError = `HTTP ${response.status}: ${errorText}`;
                            addLog(`❌ 端点 ${endpoint} 失败: ${lastError}`);
                        }
                    } catch (error) {
                        lastError = error.message;
                        addLog(`❌ 端点 ${endpoint} 错误: ${lastError}`);
                    }
                }

                throw new Error(`所有上传端点都失败，最后错误: ${lastError}`);

            } catch (error) {
                throw new Error(`文件上传失败: ${error.message}`);
            }
        }

        // 生成内容
        async function generateContent() {
            if (!currentWorkflow || !currentServerUrl) {
                alert('请先配置服务器和工作流');
                return;
            }

            try {
                const generateBtn = document.getElementById('generate-btn');
                generateBtn.disabled = true;
                generateBtn.textContent = '生成中...';

                addLog('开始生成流程...');
                updateStatus('准备上传文件...', 25);

                // 上传所有文件
                for (let i = 0; i < fileRequirements.length; i++) {
                    const requirement = fileRequirements[i];
                    const files = uploadedFiles[i] || [];

                    for (let j = 0; j < files.length; j++) {
                        const fileObj = files[j];
                        if (!fileObj.uploaded) {
                            addLog(`上传文件: ${fileObj.file.name}`);

                            try {
                                const serverName = await uploadFileToServer(fileObj.file, requirement.type);
                                fileObj.serverName = serverName;
                                fileObj.uploaded = true;
                                addLog(`✅ 上传成功: ${serverName}`);
                            } catch (error) {
                                throw new Error(`文件 ${fileObj.file.name} 上传失败: ${error.message}`);
                            }
                        }
                    }
                }

                updateStatus('准备工作流...', 50);

                // 准备工作流
                const workflow = JSON.parse(JSON.stringify(currentWorkflow));

                // 更新文件路径
                fileRequirements.forEach((req, index) => {
                    const files = uploadedFiles[index] || [];
                    if (files.length > 0) {
                        // 使用第一个文件（如果需要支持多文件，可以在这里扩展）
                        const firstFile = files[0];
                        if (firstFile.serverName) {
                            workflow[req.nodeId].inputs[req.inputKey] = firstFile.serverName;
                        }
                    }
                });

                // 更新参数
                updateWorkflowParameters(workflow);

                addLog('提交工作流到服务器...');
                updateStatus('提交工作流...', 60);

                // 提交工作流
                const promptResponse = await fetch(`${currentServerUrl}/prompt`, {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: workflow,
                        client_id: 'universal_client_' + Date.now()
                    })
                });

                if (!promptResponse.ok) {
                    throw new Error(`工作流提交失败: HTTP ${promptResponse.status}`);
                }

                const promptResult = await promptResponse.json();
                currentPromptId = promptResult.prompt_id;

                addLog(`✅ 工作流已提交，ID: ${currentPromptId}`);
                updateStatus('执行中...', 70);

                // 监控进度
                await monitorProgress(currentPromptId);

            } catch (error) {
                addLog(`❌ 生成失败: ${error.message}`);
                updateStatus('生成失败', 0);
                alert(`生成失败: ${error.message}`);
            } finally {
                const generateBtn = document.getElementById('generate-btn');
                generateBtn.disabled = false;
                generateBtn.textContent = '🚀 开始生成';
            }
        }

        // 更新工作流参数
        function updateWorkflowParameters(workflow) {
            for (const nodeId in workflow) {
                const node = workflow[nodeId];
                const inputs = node.inputs || {};

                for (const key in inputs) {
                    // 检查普通参数
                    const paramId = `param-${nodeId}-${key}`;
                    const paramElement = document.getElementById(paramId);

                    if (paramElement) {
                        const value = paramElement.value;
                        if (paramElement.type === 'number') {
                            workflow[nodeId].inputs[key] = parseFloat(value) || 0;
                        } else {
                            workflow[nodeId].inputs[key] = value;
                        }
                        addLog(`更新参数 ${nodeId}.${key}: ${value}`);
                    }

                    // 检查提示词参数
                    const promptId = `prompt-${nodeId}-${key}`;
                    const promptElement = document.getElementById(promptId);

                    if (promptElement) {
                        const value = promptElement.value;
                        workflow[nodeId].inputs[key] = value;
                        addLog(`更新提示词 ${nodeId}.${key}: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`);
                    }
                }
            }
        }

        // 监控进度
        async function monitorProgress(promptId) {
            try {
                let completed = false;
                let progress = 70;

                while (!completed) {
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // 检查队列状态
                    const queueResponse = await fetch(`${currentServerUrl}/queue`, {
                        method: 'GET',
                        mode: 'cors',
                        headers: {
                            'Accept': 'application/json',
                        }
                    });

                    if (!queueResponse.ok) {
                        throw new Error('无法获取队列状态');
                    }

                    const queueData = await queueResponse.json();

                    const isRunning = queueData.queue_running.some(item => item[1] === promptId);
                    const isPending = queueData.queue_pending.some(item => item[1] === promptId);

                    if (isRunning) {
                        progress = Math.min(progress + 2, 90);
                        updateStatus('正在生成...', progress);
                        addLog('工作流执行中...');
                    } else if (!isPending) {
                        completed = true;
                        updateStatus('获取结果...', 95);
                        addLog('工作流执行完成，获取结果...');

                        // 获取结果
                        const historyResponse = await fetch(`${currentServerUrl}/history/${promptId}`, {
                            method: 'GET',
                            mode: 'cors',
                            headers: {
                                'Accept': 'application/json',
                            }
                        });

                        if (historyResponse.ok) {
                            const historyData = await historyResponse.json();
                            if (historyData[promptId]) {
                                displayResults(historyData[promptId].outputs);
                                updateStatus('✅ 生成完成！', 100);
                                addLog('🎉 内容生成成功！');
                            } else {
                                throw new Error('无法获取生成结果');
                            }
                        } else {
                            throw new Error('获取历史记录失败');
                        }
                    } else {
                        addLog('队列等待中...');
                        updateStatus('队列等待...', progress);
                    }
                }

            } catch (error) {
                addLog(`❌ 监控失败: ${error.message}`);
                updateStatus('监控失败', 0);
                throw error;
            }
        }

        // 显示结果
        function displayResults(outputs) {
            const resultImages = document.getElementById('result-images');
            resultImages.innerHTML = '';

            let hasResults = false;

            addLog('🔍 分析生成结果...');
            addLog(`输出节点数量: ${Object.keys(outputs).length}`);

            for (const nodeId in outputs) {
                const nodeOutput = outputs[nodeId];
                addLog(`节点 ${nodeId} 输出类型: ${Object.keys(nodeOutput).join(', ')}`);

                // 处理图片输出
                if (nodeOutput.images) {
                    addLog(`节点 ${nodeId} 包含 ${nodeOutput.images.length} 张图片`);
                    nodeOutput.images.forEach((imageInfo, index) => {
                        hasResults = true;
                        const imageDiv = document.createElement('div');
                        imageDiv.className = 'result-image';

                        const img = document.createElement('img');
                        img.src = `${currentServerUrl}/view?filename=${imageInfo.filename}&type=${imageInfo.type}&subfolder=${imageInfo.subfolder || ''}`;
                        img.alt = '生成的图片';
                        img.onclick = () => downloadFile(img.src, imageInfo.filename);
                        img.title = '点击下载图片';

                        // 添加文件信息标签
                        const infoLabel = document.createElement('div');
                        infoLabel.style.cssText = 'position: absolute; top: 5px; left: 5px; background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;';
                        infoLabel.textContent = '📸 图片';

                        imageDiv.style.position = 'relative';
                        imageDiv.appendChild(img);
                        imageDiv.appendChild(infoLabel);
                        resultImages.appendChild(imageDiv);

                        addLog(`📸 生成图片: ${imageInfo.filename}`);
                    });
                }

                // 处理视频输出 - 扩展检测方式
                if (nodeOutput.videos || nodeOutput.gifs || nodeOutput.mp4s) {
                    const videoOutputs = nodeOutput.videos || nodeOutput.gifs || nodeOutput.mp4s || [];
                    addLog(`节点 ${nodeId} 包含 ${videoOutputs.length} 个视频文件`);

                    videoOutputs.forEach((videoInfo, index) => {
                        hasResults = true;
                        const videoDiv = document.createElement('div');
                        videoDiv.className = 'result-image';

                        const video = document.createElement('video');
                        video.src = `${currentServerUrl}/view?filename=${videoInfo.filename}&type=${videoInfo.type}&subfolder=${videoInfo.subfolder || ''}`;
                        video.controls = true;
                        video.preload = 'metadata';
                        video.onclick = () => downloadFile(video.src, videoInfo.filename);
                        video.title = '点击下载视频';
                        video.style.maxHeight = '300px';

                        // 添加文件信息标签
                        const infoLabel = document.createElement('div');
                        infoLabel.style.cssText = 'position: absolute; top: 5px; left: 5px; background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;';
                        infoLabel.textContent = '🎬 视频';

                        videoDiv.style.position = 'relative';
                        videoDiv.appendChild(video);
                        videoDiv.appendChild(infoLabel);
                        resultImages.appendChild(videoDiv);

                        addLog(`🎬 生成视频: ${videoInfo.filename}`);
                    });
                }

                // 检查其他可能的输出格式
                for (const outputKey in nodeOutput) {
                    if (outputKey !== 'images' && outputKey !== 'videos' && outputKey !== 'gifs' && outputKey !== 'mp4s') {
                        const outputData = nodeOutput[outputKey];
                        if (Array.isArray(outputData) && outputData.length > 0) {
                            addLog(`🔍 发现其他输出类型 "${outputKey}": ${outputData.length} 个文件`);

                            outputData.forEach((fileInfo, index) => {
                                if (fileInfo.filename) {
                                    hasResults = true;
                                    const fileDiv = document.createElement('div');
                                    fileDiv.className = 'result-image';

                                    // 根据文件扩展名判断类型
                                    const filename = fileInfo.filename.toLowerCase();
                                    const isVideo = filename.endsWith('.mp4') || filename.endsWith('.avi') ||
                                                  filename.endsWith('.mov') || filename.endsWith('.gif') ||
                                                  filename.endsWith('.webm');

                                    if (isVideo) {
                                        const video = document.createElement('video');
                                        video.src = `${currentServerUrl}/view?filename=${fileInfo.filename}&type=${fileInfo.type}&subfolder=${fileInfo.subfolder || ''}`;
                                        video.controls = true;
                                        video.preload = 'metadata';
                                        video.onclick = () => downloadFile(video.src, fileInfo.filename);
                                        video.title = '点击下载视频';
                                        video.style.maxHeight = '300px';

                                        const infoLabel = document.createElement('div');
                                        infoLabel.style.cssText = 'position: absolute; top: 5px; left: 5px; background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;';
                                        infoLabel.textContent = `🎬 ${outputKey}`;

                                        fileDiv.style.position = 'relative';
                                        fileDiv.appendChild(video);
                                        fileDiv.appendChild(infoLabel);

                                        addLog(`🎬 生成视频 (${outputKey}): ${fileInfo.filename}`);
                                    } else {
                                        const img = document.createElement('img');
                                        img.src = `${currentServerUrl}/view?filename=${fileInfo.filename}&type=${fileInfo.type}&subfolder=${fileInfo.subfolder || ''}`;
                                        img.alt = '生成的文件';
                                        img.onclick = () => downloadFile(img.src, fileInfo.filename);
                                        img.title = '点击下载文件';

                                        const infoLabel = document.createElement('div');
                                        infoLabel.style.cssText = 'position: absolute; top: 5px; left: 5px; background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;';
                                        infoLabel.textContent = `📄 ${outputKey}`;

                                        fileDiv.style.position = 'relative';
                                        fileDiv.appendChild(img);
                                        fileDiv.appendChild(infoLabel);

                                        addLog(`📄 生成文件 (${outputKey}): ${fileInfo.filename}`);
                                    }

                                    resultImages.appendChild(fileDiv);
                                }
                            });
                        }
                    }
                }
            }

            if (!hasResults) {
                resultImages.innerHTML = '<div style="text-align: center; color: #666; padding: 30px;"><p>未找到生成的内容</p><p style="font-size: 12px; margin-top: 10px;">请检查工作流是否包含输出节点</p></div>';
                addLog('⚠️ 未找到任何输出结果');
            } else {
                addLog(`✅ 共找到 ${resultImages.children.length} 个结果文件`);
            }
        }

        // 下载文件
        async function downloadFile(fileUrl, filename) {
            try {
                const response = await fetch(fileUrl);
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = filename || 'generated_file';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                addLog(`💾 下载文件: ${filename}`);
            } catch (error) {
                addLog(`❌ 下载失败: ${error.message}`);
                alert('下载失败，请右键保存文件');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 通用 ComfyUI 工作流管理系统已就绪');
            addLog('📋 使用步骤：');
            addLog('1. 输入服务器地址并测试连接');
            addLog('2. 上传工作流 JSON 文件');
            addLog('3. 根据提示上传所需文件');
            addLog('4. 调整参数（可选）');
            addLog('5. 点击开始生成');

            updateStatus('等待配置...', 0);
            updateGenerateButton();

            // 监听服务器地址变化
            document.getElementById('server-url').addEventListener('input', function() {
                currentServerUrl = '';
                updateConnectionStatus('disconnected');
                updateGenerateButton();
            });
        });
    </script>
</body>
</html>
    </script>
</body>
</html>
