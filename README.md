# ComfyUI API 网页控制台

这是一个基于您的 ComfyUI 工作流的网页控制台，可以直接通过浏览器调用 ComfyUI API 生成电商产品背景图片。

## 🚀 快速开始

1. 确保您的 ComfyUI 正在运行
2. 打开 `index.html` 文件
3. 上传产品图片
4. 设置背景描述
5. 点击生成

## ⚠️ 常见问题解决

### CORS 跨域问题

如果遇到 "Failed to fetch" 错误，这通常是 CORS（跨域资源共享）问题。

#### 解决方案 1：启动 ComfyUI 时添加 CORS 参数

```bash
# Windows
python main.py --enable-cors-header

# 或者如果您使用的是其他启动方式
python main.py --enable-cors-header --listen 0.0.0.0 --port 8188
```

#### 解决方案 2：修改 ComfyUI 配置

在 ComfyUI 的 `main.py` 或相关配置文件中添加 CORS 头：

```python
# 在响应头中添加
response.headers['Access-Control-Allow-Origin'] = '*'
response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
```

#### 解决方案 3：使用浏览器扩展

安装 CORS 浏览器扩展（仅用于开发测试）：
- Chrome: "CORS Unblock" 或 "Disable CORS"
- Firefox: "CORS Everywhere"

⚠️ **注意**：浏览器扩展方案仅适用于开发测试，不建议在生产环境使用。

### 网络连接问题

1. **检查 URL 地址**
   - 确认 ComfyUI 地址是否正确
   - 确认端口号是否正确（默认 8188）

2. **检查 ComfyUI 状态**
   - 确认 ComfyUI 正在运行
   - 在浏览器中直接访问 ComfyUI 地址测试

3. **防火墙设置**
   - 确认防火墙没有阻止连接
   - 确认网络代理设置

## 🔧 技术说明

### API 端点

- **上传图片**: `POST /upload/image`
- **提交工作流**: `POST /prompt`
- **查看队列**: `GET /queue`
- **查看历史**: `GET /history/{prompt_id}`
- **查看图片**: `GET /view?filename={filename}&type={type}`

### 工作流参数

- **采样步数**: 控制生成质量，建议 8-20
- **CFG 强度**: 控制提示词遵循程度，建议 1-7
- **Flux 引导**: Flux 模型特有参数，建议 2.5
- **随机种子**: 控制生成的随机性

## 📁 文件说明

- `index.html`: 主要的网页控制台
- `电商产品添加或者更换背景.json`: ComfyUI 工作流配置
- `README.md`: 说明文档

## 🎯 使用技巧

1. **图片格式**: 支持 JPG、PNG、WebP 格式
2. **图片大小**: 建议不超过 10MB
3. **提示词**: 使用中文描述，系统会自动处理
4. **参数调整**: 可以根据效果调整生成参数

## 🐛 故障排除

### 上传失败
- 检查图片格式和大小
- 确认网络连接
- 查看浏览器控制台错误信息

### 生成失败
- 检查 ComfyUI 模型是否加载完成
- 确认工作流配置正确
- 查看 ComfyUI 控制台日志

### 进度卡住
- 刷新页面重试
- 检查 ComfyUI 队列状态
- 重启 ComfyUI 服务

## 📞 技术支持

如果遇到问题：

1. 首先点击"测试连接"按钮检查连接状态
2. 查看页面底部的执行日志
3. 检查浏览器开发者工具的控制台
4. 确认 ComfyUI 服务正常运行

## 🔄 更新日志

- v1.0: 初始版本，支持基本的图片生成功能
- v1.1: 添加 CORS 支持和错误处理
- v1.2: 改进用户界面和状态监控
